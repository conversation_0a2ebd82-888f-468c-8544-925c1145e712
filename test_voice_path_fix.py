#!/usr/bin/env python3
"""
测试语音路径修复
验证路径配置是否正确
"""

import os
import re

def test_audio_path_fix():
    """测试音频路径修复"""
    print("🔍 测试语音路径修复...")
    
    # 检查 main.cpp 中的 audio_path 定义
    main_cpp_path = "src/main.cpp"
    if not os.path.exists(main_cpp_path):
        print("❌ 找不到 src/main.cpp 文件")
        return False
    
    with open(main_cpp_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找 audio_path 定义
    audio_path_match = re.search(r'String\s+audio_path\s*=\s*"([^"]*)"', content)
    if not audio_path_match:
        print("❌ 找不到 audio_path 定义")
        return False
    
    audio_path_value = audio_path_match.group(1)
    print(f"📁 发现 audio_path 定义: \"{audio_path_value}\"")
    
    # 检查是否为相对路径（不以 / 开头）
    if audio_path_value.startswith('/'):
        print("❌ audio_path 仍然是绝对路径，应该是相对路径")
        return False
    elif audio_path_value == "audio":
        print("✅ audio_path 已正确设置为相对路径")
    else:
        print(f"⚠️  audio_path 值为 \"{audio_path_value}\"，预期为 \"audio\"")
        return False
    
    # 检查 main.h 中的 BASE_FILE_PATH 定义
    main_h_path = "include/main.h"
    if not os.path.exists(main_h_path):
        print("❌ 找不到 include/main.h 文件")
        return False
    
    with open(main_h_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找 BASE_FILE_PATH 定义
    base_path_match = re.search(r'#define\s+BASE_FILE_PATH\s+"([^"]*)"', content)
    if not base_path_match:
        print("❌ 找不到 BASE_FILE_PATH 定义")
        return False
    
    base_path_value = base_path_match.group(1)
    print(f"📁 发现 BASE_FILE_PATH 定义: \"{base_path_value}\"")
    
    if base_path_value == "/HealthHub":
        print("✅ BASE_FILE_PATH 正确设置为 \"/HealthHub\"")
    else:
        print(f"⚠️  BASE_FILE_PATH 值为 \"{base_path_value}\"，预期为 \"/HealthHub\"")
    
    # 检查 LittleFS.begin 调用
    littlefs_match = re.search(r'LittleFS\.begin\([^)]*BASE_FILE_PATH[^)]*\)', content)
    if littlefs_match:
        print("✅ 发现 LittleFS.begin 使用 BASE_FILE_PATH")
        print(f"   调用: {littlefs_match.group(0)}")
    else:
        # 在 main.cpp 中查找
        with open(main_cpp_path, 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        littlefs_match = re.search(r'LittleFS\.begin\([^)]*BASE_FILE_PATH[^)]*\)', main_content)
        if littlefs_match:
            print("✅ 发现 LittleFS.begin 使用 BASE_FILE_PATH")
            print(f"   调用: {littlefs_match.group(0)}")
        else:
            print("⚠️  未找到 LittleFS.begin 使用 BASE_FILE_PATH 的调用")
    
    print("\n📊 路径配置分析:")
    print(f"   LittleFS 挂载点: {base_path_value}")
    print(f"   音频相对路径: {audio_path_value}")
    print(f"   实际音频路径: {base_path_value}/{audio_path_value}")
    print(f"   示例文件路径: {base_path_value}/{audio_path_value}/select_user.wav")
    print(f"   版本文件路径: {base_path_value}/{audio_path_value}/.select_user.version")
    
    return True

def test_voice_smart_switch_fix():
    """测试 voice_smart_switch.cpp 中的修复"""
    print("\n🔍 测试 voice_smart_switch.cpp 修复...")
    
    file_path = "src/voice_smart_switch.cpp"
    if not os.path.exists(file_path):
        print("❌ 找不到 src/voice_smart_switch.cpp 文件")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查 select_best_source 函数签名
    func_signature_match = re.search(
        r'static\s+voice_error_t\s+select_best_source\s*\([^)]*const\s+char\s*\*\s*voice_name[^)]*\)',
        content
    )
    if func_signature_match:
        print("✅ select_best_source 函数已添加 voice_name 参数")
    else:
        print("❌ select_best_source 函数签名未正确修改")
        return False
    
    # 检查文件路径设置是否使用了 voice_name
    hardcoded_voice_name = re.search(r'"voice_name"', content)
    if hardcoded_voice_name:
        print("❌ 仍然存在硬编码的 \"voice_name\" 字符串")
        return False
    else:
        print("✅ 已移除硬编码的 \"voice_name\" 字符串")
    
    # 检查版本文件处理
    version_check = re.search(r'LittleFS\.exists\(version_file\)', content)
    if version_check:
        print("✅ 版本文件处理已改为检查文件存在性")
    else:
        print("⚠️  未找到版本文件存在性检查")
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🧪 语音路径修复测试")
    print("=" * 50)
    
    success = True
    
    # 测试路径修复
    if not test_audio_path_fix():
        success = False
    
    # 测试 voice_smart_switch 修复
    if not test_voice_smart_switch_fix():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 所有测试通过！路径修复看起来正确。")
        print("\n📋 修复总结:")
        print("1. ✅ audio_path 从绝对路径改为相对路径")
        print("2. ✅ select_best_source 函数添加了 voice_name 参数")
        print("3. ✅ 移除了硬编码的文件名")
        print("4. ✅ 改进了版本文件处理逻辑")
        print("\n🎯 预期效果:")
        print("- 语音文件路径: /HealthHub/audio/select_user.wav")
        print("- 版本文件路径: /HealthHub/audio/.select_user.version")
        print("- 版本文件不存在时不会报错")
    else:
        print("❌ 部分测试失败，请检查修复。")
    
    return success

if __name__ == "__main__":
    main()
