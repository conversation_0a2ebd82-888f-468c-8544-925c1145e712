/**
 * @file test_voice_fix_behavior.cpp
 * @brief 测试语音路径修复后的行为
 * 
 * 这个测试文件可以用来验证修复后的语音系统是否正常工作
 */

#include <Arduino.h>
#include <LittleFS.h>
#include "voice_hybrid_system.h"

// 模拟外部变量
String audio_path = "audio";
voice_system_config_t system_config = {
    .priority_policy = VOICE_PRIORITY_QUALITY_FIRST,
    .cache_size_kb = 64,
    .compression_enabled = true,
    .auto_update_enabled = false,
    .update_check_interval = 3600
};

/**
 * @brief 测试路径构建
 */
void test_path_construction() {
    Serial.println("=== 测试路径构建 ===");
    
    const char* voice_name = "select_user";
    
    // 测试音频文件路径
    String audio_file_path = audio_path + "/" + String(voice_name) + ".wav";
    Serial.printf("音频文件路径: %s\n", audio_file_path.c_str());
    
    // 测试版本文件路径
    String version_file_path = audio_path + "/." + String(voice_name) + ".version";
    Serial.printf("版本文件路径: %s\n", version_file_path.c_str());
    
    // 在 LittleFS 挂载点 /HealthHub 下，实际路径应该是：
    Serial.println("预期实际路径:");
    Serial.printf("  音频文件: /HealthHub/%s\n", audio_file_path.c_str());
    Serial.printf("  版本文件: /HealthHub/%s\n", version_file_path.c_str());
}

/**
 * @brief 测试文件存在性检查
 */
void test_file_existence() {
    Serial.println("\n=== 测试文件存在性检查 ===");
    
    const char* voice_name = "select_user";
    String audio_file_path = audio_path + "/" + String(voice_name) + ".wav";
    String version_file_path = audio_path + "/." + String(voice_name) + ".version";
    
    // 检查音频文件
    bool audio_exists = LittleFS.exists(audio_file_path);
    Serial.printf("音频文件存在: %s\n", audio_exists ? "是" : "否");
    
    // 检查版本文件
    bool version_exists = LittleFS.exists(version_file_path);
    Serial.printf("版本文件存在: %s\n", version_exists ? "是" : "否");
    
    if (!version_exists) {
        Serial.println("版本文件不存在，将使用默认版本 1");
    }
}

/**
 * @brief 测试语音文件信息获取
 */
void test_voice_file_info() {
    Serial.println("\n=== 测试语音文件信息获取 ===");
    
    const char* voice_name = "select_user";
    voice_file_info_t file_info;
    
    voice_error_t err = voice_get_file_info(voice_name, &file_info);
    
    if (err == VOICE_ERR_OK) {
        Serial.printf("✅ 成功获取语音文件信息:\n");
        Serial.printf("  源类型: %d\n", file_info.source);
        Serial.printf("  文件路径: %s\n", file_info.file_path);
        Serial.printf("  文件大小: %u bytes\n", file_info.file_size);
        Serial.printf("  版本: %u\n", file_info.version);
        Serial.printf("  是否压缩: %s\n", file_info.is_compressed ? "是" : "否");
    } else {
        Serial.printf("❌ 获取语音文件信息失败，错误代码: %d\n", err);
        
        // 打印可能的错误原因
        switch (err) {
            case VOICE_ERR_FILE_NOT_FOUND:
                Serial.println("  原因: 文件未找到");
                break;
            case VOICE_ERR_INVALID_PARAM:
                Serial.println("  原因: 参数无效");
                break;
            default:
                Serial.println("  原因: 未知错误");
                break;
        }
    }
}

/**
 * @brief 测试智能播放功能
 */
void test_smart_play() {
    Serial.println("\n=== 测试智能播放功能 ===");
    
    const char* voice_name = "select_user";
    
    Serial.printf("尝试播放语音: %s\n", voice_name);
    voice_error_t err = voice_play_smart(voice_name);
    
    if (err == VOICE_ERR_OK) {
        Serial.println("✅ 智能播放成功启动");
    } else {
        Serial.printf("❌ 智能播放失败，错误代码: %d\n", err);
        
        // 打印错误信息
        switch (err) {
            case VOICE_ERR_FILE_NOT_FOUND:
                Serial.println("  原因: 所有源都找不到该语音文件");
                break;
            case VOICE_ERR_INVALID_PARAM:
                Serial.println("  原因: 参数无效");
                break;
            case VOICE_ERR_INIT_FAILED:
                Serial.println("  原因: 播放初始化失败");
                break;
            default:
                Serial.println("  原因: 未知错误");
                break;
        }
    }
}

/**
 * @brief 测试版本文件处理
 */
void test_version_file_handling() {
    Serial.println("\n=== 测试版本文件处理 ===");
    
    const char* voice_name = "test_voice";
    String version_file_path = audio_path + "/." + String(voice_name) + ".version";
    
    // 测试版本文件不存在的情况
    if (!LittleFS.exists(version_file_path)) {
        Serial.println("版本文件不存在，测试默认版本处理...");
        
        uint16_t version = 1; // 默认版本
        Serial.printf("使用默认版本: %u\n", version);
        Serial.println("✅ 版本文件不存在时不会报错");
    } else {
        Serial.println("版本文件存在，测试读取...");
        
        File ver_file = LittleFS.open(version_file_path, "r");
        if (ver_file) {
            String version_str = ver_file.readString();
            version_str.trim();
            
            uint16_t version = 1;
            if (version_str.length() > 0) {
                version = version_str.toInt();
                if (version == 0) version = 1;
            }
            
            Serial.printf("读取到版本: %u\n", version);
            ver_file.close();
            Serial.println("✅ 版本文件读取成功");
        } else {
            Serial.println("❌ 版本文件打开失败");
        }
    }
}

/**
 * @brief 主测试函数
 */
void run_voice_fix_tests() {
    Serial.println("开始语音路径修复测试...\n");
    
    // 初始化 LittleFS（模拟实际环境）
    if (!LittleFS.begin(true, "/HealthHub")) {
        Serial.println("❌ LittleFS 初始化失败");
        return;
    }
    
    Serial.println("✅ LittleFS 初始化成功，挂载点: /HealthHub\n");
    
    // 运行各项测试
    test_path_construction();
    test_file_existence();
    test_version_file_handling();
    
    // 如果语音系统已初始化，运行更多测试
    if (voice_hybrid_init() == VOICE_ERR_OK) {
        Serial.println("\n✅ 语音混合系统初始化成功");
        test_voice_file_info();
        test_smart_play();
    } else {
        Serial.println("\n⚠️  语音混合系统初始化失败，跳过相关测试");
    }
    
    Serial.println("\n=== 测试完成 ===");
}

// 如果这是一个独立的测试程序
#ifdef VOICE_FIX_TEST_STANDALONE
void setup() {
    Serial.begin(115200);
    delay(1000);
    
    Serial.println("语音路径修复行为测试");
    Serial.println("====================");
    
    run_voice_fix_tests();
}

void loop() {
    // 空循环
    delay(1000);
}
#endif
