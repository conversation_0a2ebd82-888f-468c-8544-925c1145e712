/**
 * @file test_voice_smart_fix.cpp
 * @brief 测试语音智能播放修复效果
 * 
 * 这个文件包含了测试代码，用于验证语音智能播放系统的修复效果
 */

#include <Arduino.h>
#include "voice_hybrid_system.h"

static const char* TAG = "VoiceSmartTest";

// 外部声明
extern bool system_initialized;
extern String audio_path;
extern uint8_t audio_enable_flag;

/**
 * @brief 测试语音智能播放修复
 */
void test_voice_smart_fix() {
    Serial.println("\n=== 测试语音智能播放修复 ===");
    
    // 1. 测试系统初始化状态
    Serial.printf("语音混合系统初始化状态: %s\n", system_initialized ? "已初始化" : "未初始化");
    
    // 2. 测试音频路径配置
    Serial.printf("音频路径配置: %s\n", audio_path.c_str());
    
    // 3. 测试语音启用状态
    Serial.printf("语音启用状态: %s\n", audio_enable_flag ? "启用" : "禁用");
    
    // 4. 测试文件存在性
    const char* test_voice = "select_user";
    String full_path = audio_path + "/" + String(test_voice) + ".wav";
    Serial.printf("测试文件路径: %s\n", full_path.c_str());
    
    if (LittleFS.exists(full_path)) {
        File file = LittleFS.open(full_path, "r");
        if (file) {
            Serial.printf("✅ 文件存在，大小: %d 字节\n", file.size());
            file.close();
        } else {
            Serial.println("❌ 文件存在但无法打开");
        }
    } else {
        Serial.println("❌ 文件不存在");
    }
    
    // 5. 测试语音源信息获取
    if (system_initialized) {
        voice_file_info_t file_info;
        voice_error_t err = voice_get_file_info(test_voice, &file_info);
        
        if (err == VOICE_ERR_OK) {
            Serial.printf("✅ 语音源信息获取成功\n");
            Serial.printf("   源类型: %d\n", file_info.source);
            Serial.printf("   文件路径: %s\n", file_info.file_path);
            Serial.printf("   文件大小: %d 字节\n", file_info.file_size);
        } else {
            Serial.printf("❌ 语音源信息获取失败，错误代码: %d\n", err);
        }
    }
    
    // 6. 测试智能播放
    Serial.println("\n--- 测试智能播放 ---");
    if (system_initialized) {
        voice_error_t err = voice_play_smart(test_voice);
        if (err == VOICE_ERR_OK) {
            Serial.printf("✅ 智能播放启动成功: %s\n", test_voice);
        } else {
            Serial.printf("❌ 智能播放失败，错误代码: %d\n", err);
            
            // 打印详细错误信息
            switch (err) {
                case VOICE_ERR_INVALID_PARAM:
                    Serial.println("   错误原因: 参数无效");
                    break;
                case VOICE_ERR_FILE_NOT_FOUND:
                    Serial.println("   错误原因: 文件未找到");
                    break;
                case VOICE_ERR_INIT_FAILED:
                    Serial.println("   错误原因: 初始化失败");
                    break;
                default:
                    Serial.println("   错误原因: 未知错误");
                    break;
            }
        }
    } else {
        Serial.println("⚠️  语音混合系统未初始化，跳过智能播放测试");
    }
    
    Serial.println("=== 测试完成 ===\n");
}

/**
 * @brief 测试传统播放方式
 */
void test_traditional_play() {
    Serial.println("\n=== 测试传统播放方式 ===");
    
    const char* test_voice = "select_user";
    
    // 调用传统播放函数
    extern void audio_prompt_traditional(const char *voice_name);
    audio_prompt_traditional(test_voice);
    
    Serial.printf("✅ 传统播放方式调用完成: %s\n", test_voice);
    Serial.println("=== 测试完成 ===\n");
}

/**
 * @brief 测试audio_prompt_smart函数
 */
void test_audio_prompt_smart() {
    Serial.println("\n=== 测试audio_prompt_smart函数 ===");
    
    const char* test_voice = "select_user";
    
    // 调用智能播放函数
    extern void audio_prompt_smart(const char *voice_name);
    audio_prompt_smart(test_voice);
    
    Serial.printf("✅ audio_prompt_smart调用完成: %s\n", test_voice);
    Serial.println("=== 测试完成 ===\n");
}

/**
 * @brief 运行所有测试
 */
void run_all_voice_tests() {
    Serial.println("\n🔧 开始语音智能播放修复测试...\n");
    
    // 等待系统稳定
    delay(2000);
    
    // 运行各项测试
    test_voice_smart_fix();
    delay(1000);
    
    test_traditional_play();
    delay(1000);
    
    test_audio_prompt_smart();
    delay(1000);
    
    Serial.println("🎉 所有测试完成！");
}

/**
 * @brief 打印系统状态信息
 */
void print_voice_system_status() {
    Serial.println("\n=== 语音系统状态 ===");
    
    // 基本状态
    Serial.printf("系统初始化: %s\n", system_initialized ? "✅" : "❌");
    Serial.printf("音频启用: %s\n", audio_enable_flag ? "✅" : "❌");
    Serial.printf("音频路径: %s\n", audio_path.c_str());
    
    // 存储信息
    if (system_initialized) {
        voice_storage_info_t storage_info;
        if (voice_get_storage_info(&storage_info) == VOICE_ERR_OK) {
            Serial.printf("ROM存储: %d/%d 字节 (%d 文件)\n", 
                         storage_info.rom_used_size, 
                         storage_info.rom_total_size,
                         storage_info.rom_file_count);
            Serial.printf("LittleFS存储: %d/%d 字节 (%d 文件)\n", 
                         storage_info.littlefs_used_size, 
                         storage_info.littlefs_total_size,
                         storage_info.littlefs_file_count);
            Serial.printf("缓存: %d/%d 字节\n", 
                         storage_info.cache_used_size, 
                         storage_info.cache_total_size);
        }
    }
    
    Serial.println("=== 状态信息结束 ===\n");
}
