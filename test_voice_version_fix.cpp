/**
 * @file test_voice_version_fix.cpp
 * @brief 测试语音版本文件修复效果
 * 
 * 这个测试文件用于验证语音版本文件修复是否正常工作
 */

#include <Arduino.h>
#include <LittleFS.h>
#include <esp_log.h>
#include "voice_hybrid_system.h"

static const char *TAG = "VoiceVersionTest";

// 外部声明
extern String audio_path;
extern String wav_file_path[10];

/**
 * @brief 测试版本文件创建功能
 */
bool test_version_file_creation()
{
    ESP_LOGI(TAG, "Testing version file creation...");
    
    const char *test_voice_names[] = {
        "open_app_to_config",
        "network_success", 
        "select_user",
        "blood_pressure_data",
        "temperature_data"
    };
    
    bool all_tests_passed = true;
    
    for (int i = 0; i < 5; i++)
    {
        String audio_file = audio_path + "/" + wav_file_path[i] + ".wav";
        String version_file = audio_path + "/." + wav_file_path[i] + ".version";
        
        ESP_LOGI(TAG, "Testing %s...", test_voice_names[i]);
        
        // 检查音频文件是否存在
        if (LittleFS.exists(audio_file))
        {
            ESP_LOGI(TAG, "  ✓ Audio file exists: %s", audio_file.c_str());
            
            // 检查版本文件是否存在
            if (LittleFS.exists(version_file))
            {
                ESP_LOGI(TAG, "  ✓ Version file exists: %s", version_file.c_str());
                
                // 读取版本号
                File ver_file = LittleFS.open(version_file, "r");
                if (ver_file)
                {
                    String version_str = ver_file.readString();
                    version_str.trim();
                    uint16_t version = version_str.toInt();
                    ver_file.close();
                    
                    ESP_LOGI(TAG, "  ✓ Version: %d", version);
                    
                    if (version > 0)
                    {
                        ESP_LOGI(TAG, "  ✓ Valid version number");
                    }
                    else
                    {
                        ESP_LOGE(TAG, "  ✗ Invalid version number: %d", version);
                        all_tests_passed = false;
                    }
                }
                else
                {
                    ESP_LOGE(TAG, "  ✗ Failed to read version file");
                    all_tests_passed = false;
                }
            }
            else
            {
                ESP_LOGW(TAG, "  ! Version file not found: %s", version_file.c_str());
                ESP_LOGI(TAG, "    This is expected for new installations");
            }
        }
        else
        {
            ESP_LOGW(TAG, "  ! Audio file not found: %s", audio_file.c_str());
            ESP_LOGI(TAG, "    This is expected if audio files haven't been uploaded yet");
        }
        
        ESP_LOGI(TAG, "");
    }
    
    return all_tests_passed;
}

/**
 * @brief 测试语音文件信息获取
 */
bool test_voice_file_info()
{
    ESP_LOGI(TAG, "Testing voice file info retrieval...");
    
    const char *test_voice_names[] = {
        "select_user",
        "network_success",
        "open_app_to_config"
    };
    
    bool all_tests_passed = true;
    
    for (int i = 0; i < 3; i++)
    {
        ESP_LOGI(TAG, "Testing voice_get_file_info for: %s", test_voice_names[i]);
        
        voice_file_info_t file_info;
        voice_error_t err = voice_get_file_info(test_voice_names[i], &file_info);
        
        if (err == VOICE_ERR_OK)
        {
            ESP_LOGI(TAG, "  ✓ Successfully got file info");
            ESP_LOGI(TAG, "    Source: %d", file_info.source);
            ESP_LOGI(TAG, "    Size: %d bytes", file_info.file_size);
            ESP_LOGI(TAG, "    Version: %d", file_info.version);
            ESP_LOGI(TAG, "    Quality: %.2f", file_info.quality_score);
        }
        else if (err == VOICE_ERR_FILE_NOT_FOUND)
        {
            ESP_LOGW(TAG, "  ! File not found: %s", test_voice_names[i]);
            ESP_LOGI(TAG, "    This is expected if the file hasn't been uploaded yet");
        }
        else
        {
            ESP_LOGE(TAG, "  ✗ Error getting file info: %d", err);
            all_tests_passed = false;
        }
        
        ESP_LOGI(TAG, "");
    }
    
    return all_tests_passed;
}

/**
 * @brief 测试文件系统状态
 */
void test_filesystem_status()
{
    ESP_LOGI(TAG, "Testing filesystem status...");
    
    ESP_LOGI(TAG, "LittleFS Status:");
    ESP_LOGI(TAG, "  Total bytes: %zu", LittleFS.totalBytes());
    ESP_LOGI(TAG, "  Used bytes: %zu", LittleFS.usedBytes());
    ESP_LOGI(TAG, "  Free bytes: %zu", LittleFS.totalBytes() - LittleFS.usedBytes());
    
    // 检查音频目录
    if (LittleFS.exists(audio_path))
    {
        ESP_LOGI(TAG, "  ✓ Audio directory exists: %s", audio_path.c_str());
        
        // 列出音频目录中的文件
        File root = LittleFS.open(audio_path);
        if (root && root.isDirectory())
        {
            ESP_LOGI(TAG, "  Audio directory contents:");
            File file = root.openNextFile();
            int file_count = 0;
            while (file)
            {
                if (!file.isDirectory())
                {
                    ESP_LOGI(TAG, "    %s (%d bytes)", file.name(), file.size());
                    file_count++;
                }
                file = root.openNextFile();
            }
            ESP_LOGI(TAG, "  Total files in audio directory: %d", file_count);
        }
        else
        {
            ESP_LOGE(TAG, "  ✗ Failed to open audio directory");
        }
    }
    else
    {
        ESP_LOGE(TAG, "  ✗ Audio directory does not exist: %s", audio_path.c_str());
    }
    
    ESP_LOGI(TAG, "");
}

/**
 * @brief 运行所有测试
 */
void run_voice_version_tests()
{
    ESP_LOGI(TAG, "=== Voice Version Fix Test Suite ===");
    ESP_LOGI(TAG, "");
    
    // 测试文件系统状态
    test_filesystem_status();
    
    // 测试版本文件创建
    bool creation_test = test_version_file_creation();
    
    // 测试语音文件信息获取
    bool info_test = test_voice_file_info();
    
    // 总结测试结果
    ESP_LOGI(TAG, "=== Test Results ===");
    ESP_LOGI(TAG, "Version file creation test: %s", creation_test ? "PASSED" : "FAILED");
    ESP_LOGI(TAG, "Voice file info test: %s", info_test ? "PASSED" : "FAILED");
    
    if (creation_test && info_test)
    {
        ESP_LOGI(TAG, "🎉 All tests PASSED! Voice version fix is working correctly.");
    }
    else
    {
        ESP_LOGE(TAG, "❌ Some tests FAILED. Please check the implementation.");
    }
    
    ESP_LOGI(TAG, "=== End of Test Suite ===");
}

/**
 * @brief 手动创建测试版本文件（用于测试）
 */
void create_test_version_files()
{
    ESP_LOGI(TAG, "Creating test version files...");
    
    const char *test_files[] = {
        "select_user",
        "network_success",
        "open_app_to_config"
    };
    
    for (int i = 0; i < 3; i++)
    {
        String version_file = audio_path + "/." + test_files[i] + ".version";
        
        if (!LittleFS.exists(version_file))
        {
            File ver_file = LittleFS.open(version_file, "w");
            if (ver_file)
            {
                ver_file.print("1");
                ver_file.close();
                ESP_LOGI(TAG, "Created test version file: %s", version_file.c_str());
            }
            else
            {
                ESP_LOGE(TAG, "Failed to create test version file: %s", version_file.c_str());
            }
        }
        else
        {
            ESP_LOGI(TAG, "Test version file already exists: %s", version_file.c_str());
        }
    }
}
