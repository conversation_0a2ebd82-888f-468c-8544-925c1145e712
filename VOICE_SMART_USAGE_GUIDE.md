# 语音智能播放系统使用指南

## 概述

修复后的语音智能播放系统现在具备了更好的兼容性和稳定性。系统会自动在智能播放和传统播放之间切换，确保语音播放的可靠性。

## 主要功能

### 1. 智能播放 (推荐)
- 自动选择最佳语音源（ROM/LittleFS/缓存）
- 支持多级回退机制
- 详细的错误日志和调试信息

### 2. 传统播放 (回退)
- 直接从LittleFS播放WAV文件
- 作为智能播放失败时的回退方案
- 与原有代码完全兼容

### 3. 混合播放 (自动)
- 优先尝试智能播放
- 失败时自动回退到传统播放
- 对用户透明，无需修改现有代码

## 使用方法

### 基本使用

```cpp
// 推荐的使用方式 - 智能播放
audio_prompt_smart("select_user");

// 传统方式 - 仍然支持
audio_prompt("/audio/select_user.wav");

// 直接传统播放
audio_prompt_traditional("select_user");
```

### 错误处理

```cpp
// 检查系统状态
extern bool system_initialized;
if (!system_initialized) {
    Serial.println("语音混合系统未初始化");
}

// 使用智能播放并处理错误
voice_error_t err = voice_play_smart("voice_name");
if (err != VOICE_ERR_OK) {
    Serial.printf("智能播放失败，错误代码: %d\n", err);
    // 系统会自动回退到传统播放
}
```

### 系统监控

```cpp
// 获取存储信息
voice_storage_info_t storage_info;
if (voice_get_storage_info(&storage_info) == VOICE_ERR_OK) {
    Serial.printf("ROM: %d/%d 字节\n", 
                 storage_info.rom_used_size, 
                 storage_info.rom_total_size);
    Serial.printf("LittleFS: %d/%d 字节\n", 
                 storage_info.littlefs_used_size, 
                 storage_info.littlefs_total_size);
}
```

## 配置选项

### 优先级策略

```cpp
// 速度优先（默认）
voice_set_priority_policy(VOICE_PRIORITY_SPEED_FIRST);

// 质量优先
voice_set_priority_policy(VOICE_PRIORITY_QUALITY_FIRST);

// 可靠性优先
voice_set_priority_policy(VOICE_PRIORITY_RELIABILITY_FIRST);
```

### 缓存设置

```cpp
// 设置缓存大小（KB）
voice_set_cache_size(64);

// 启用/禁用压缩
voice_set_compression_enabled(true);
```

### 日志配置

```cpp
// 配置日志系统
voice_configure_logging(
    true,           // 记录到文件
    true,           // 输出到串口
    100 * 1024,     // 最大文件大小
    ESP_LOG_INFO    // 日志级别
);
```

## 故障排除

### 常见问题

#### 1. "Invalid Parameter" 错误
**原因**: 通常是参数为空或系统未初始化
**解决**: 
- 检查传入的voice_name是否为空
- 确认语音混合系统已正确初始化
- 查看详细的错误日志

#### 2. 智能播放失败但传统播放成功
**原因**: 语音文件在LittleFS中存在但ROM中不存在
**解决**: 
- 这是正常情况，系统会自动回退
- 可以通过预加载将常用语音加载到缓存

#### 3. 所有播放方式都失败
**原因**: 语音文件不存在或文件系统问题
**解决**: 
- 检查文件是否存在于 `/HealthHub/audio/` 目录
- 确认文件格式正确（WAV格式）
- 检查LittleFS是否正常挂载

### 调试技巧

#### 1. 启用详细日志
```cpp
// 在setup()中添加
esp_log_level_set("VoiceSmart", ESP_LOG_DEBUG);
esp_log_level_set("VoiceHybrid", ESP_LOG_DEBUG);
```

#### 2. 使用测试函数
```cpp
// 运行完整的系统测试
run_all_voice_tests();

// 打印系统状态
print_voice_system_status();
```

#### 3. 检查文件系统
```cpp
// 列出音频目录中的文件
File root = LittleFS.open("/audio");
if (root && root.isDirectory()) {
    File file = root.openNextFile();
    while (file) {
        Serial.printf("文件: %s, 大小: %d\n", file.name(), file.size());
        file = root.openNextFile();
    }
}
```

## 性能优化

### 1. 预加载常用语音
```cpp
// 在setup()中预加载
voice_preload("select_user");
voice_preload("network_success");
voice_preload("open_app_to_config");
```

### 2. 合理设置缓存大小
```cpp
// 根据可用内存调整缓存大小
voice_set_cache_size(32);  // 32KB适合内存较少的情况
voice_set_cache_size(128); // 128KB适合内存充足的情况
```

### 3. 定期清理缓存
```cpp
// 在适当的时候清理缓存
voice_clear_cache();
```

## 最佳实践

1. **优先使用audio_prompt_smart()** - 它会自动处理回退
2. **检查系统状态** - 在关键操作前检查初始化状态
3. **合理配置日志** - 开发时启用详细日志，生产时适当降低级别
4. **预加载常用语音** - 提高播放响应速度
5. **监控存储空间** - 定期检查存储使用情况
6. **处理错误情况** - 虽然有自动回退，但仍应处理错误情况

## 示例代码

```cpp
void example_voice_usage() {
    // 检查系统状态
    if (!system_initialized) {
        Serial.println("语音系统未就绪");
        return;
    }
    
    // 播放语音（推荐方式）
    audio_prompt_smart("select_user");
    
    // 等待播放完成
    while (audio.isRunning()) {
        delay(100);
    }
    
    // 播放下一个语音
    audio_prompt_smart("network_success");
}
```

通过遵循这些指南，您可以充分利用修复后的语音智能播放系统，获得更稳定和可靠的语音播放体验。
